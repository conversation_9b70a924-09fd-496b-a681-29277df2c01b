<?php

namespace App\Console\Commands;

use App\Helper\ShpifyScraper;
use App\Models\ScrapShopify;

use Exception;
use Illuminate\Console\Command;


class IgeekjoScraperCommand extends Command
{

     use ShpifyScraper;
    protected $signature = 'import:igeekjo {--page=1 : Starting page} {--max-pages=100 : Maximum pages to import}';
    protected $description = 'Import products from Shopify store with Arabic and English support';

    protected string $baseUrl = 'https://igeekjo.com';
    protected int $perPage = 250;
    protected int $currentPage;
    protected int $maxPages;
 public function handle(): int
    {

        ini_set('max_execution_time', 0);
        set_time_limit(0);
        $this->currentPage = (int) $this->option('page');
        $this->maxPages = (int) $this->option('max-pages');
        $importedCount = 0;
        $round = 0;


        $scrapShopifyModel = ScrapShopify::where('site', $this->baseUrl)->first();

        if (is_null($scrapShopifyModel)) {
            $scrapShopifyModel = ScrapShopify::create(['site' => $this->baseUrl, 'pageNumber' => $this->currentPage]);
        } else {
            $currentPage = (int) ScrapShopify::where('site', $this->baseUrl)->orderBy('pageNumber', 'DESC')->first()->pageNumber;
            ScrapShopify::where('site', $this->baseUrl)->delete();
            $currentPage++;
            $scrapShopifyModel = ScrapShopify::create(['site' => $this->baseUrl, 'pageNumber' => $this->currentPage]);
        }

        $this->info("Starting import from: {$this->baseUrl}");
        $this->info("Page range: {$this->currentPage} to {$this->maxPages}");

        do {
            try {
                $response = $this->fetchProductsPage($this->currentPage);

                if ($response->failed()) {
                    $this->error("Failed to fetch page {$this->currentPage}: HTTP {$response->status()}");
                    break;
                }

                $data = $response->json();
                $round++;
                if (empty($data['products'])) {
                    $this->info("No more products found on page {$this->currentPage}");
                    break;
                }

                $this->info("Processing page {$this->currentPage} with " . count($data['products']) . " products");


                foreach ($data['products'] as $productData) {
                    $this->processProduct($productData);
                    $importedCount++;
                }

                $this->info("Successfully processed page {$this->currentPage}");
                $hasMoreProducts = count($data['products']) === $this->perPage;
                $this->currentPage++;
            } catch (Exception $e) {
                $this->error("Error processing page {$this->currentPage}: {$e->getMessage()}");
                break;
            }
        } while (
            count($data['products']) === $this->perPage &&
            (!$this->maxPages || $this->currentPage <= $this->maxPages)   &&  $round >= 5
        );


        $scrapShopifyModel = ScrapShopify::create(['site' => $this->baseUrl, 'pageNumber' => $this->currentPage]);

        $this->info("Import completed. Total products imported: {$importedCount}");
        return 0;
    }
}
